from __future__ import annotations

from DrissionPage import Chromium, ChromiumOptions
import jmespath
import json
from urllib.parse import urlsplit, parse_qsl, urlencode, urlunsplit
import time
import csv
import logging
import re
from datetime import datetime
from typing import List, Dict, Set, Union


def setup_logger(name: str = "DouyinScraper", level: int = logging.INFO) -> logging.Logger:
    """设置日志系统"""
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 文件处理器
    file_handler = logging.FileHandler(
        f'douyin_scraper_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
        encoding='utf-8'
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    return logger


def json_to_csv(json_data: Union[str, List[Dict]], csv_filename: str = None) -> str:
    """
    将 JSON 数组数据导出到 CSV 文件，自适应处理所有字段。

    Args:
        json_data: JSON 字符串或 Python 列表，包含字典数组
        csv_filename: CSV 文件名，如果为 None 则自动生成

    Returns:
        str: 生成的 CSV 文件名

    Raises:
        ValueError: 当输入数据格式不正确时
    """
    # 解析 JSON 数据
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的 JSON 格式: {e}")
    elif isinstance(json_data, list):
        data = json_data
    else:
        raise ValueError("输入数据必须是 JSON 字符串或列表")

    # 检查数据是否为空
    if not data:
        raise ValueError("输入数据为空")

    # 检查是否为字典列表
    if not all(isinstance(item, dict) for item in data):
        raise ValueError("JSON 数组中的所有元素必须是对象(字典)")

    # 自动收集所有可能的字段名
    all_fields = set()
    for item in data:
        all_fields.update(item.keys())

    # 按字母顺序排序字段，确保输出一致性
    fieldnames = sorted(all_fields)

    # 生成文件名
    if csv_filename is None:
        csv_filename = f"export_data_{int(time.time())}.csv"

    # 写入 CSV 文件
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # 写入表头
            writer.writeheader()

            # 写入数据行
            for item in data:
                # 确保每行都包含所有字段，缺失的字段用空字符串填充
                row = {field: item.get(field, '') for field in fieldnames}
                writer.writerow(row)

        print(f"CSV 文件已生成: {csv_filename}")
        print(f"包含 {len(data)} 行数据，{len(fieldnames)} 个字段")
        print(f"字段列表: {', '.join(fieldnames)}")

        return csv_filename

    except IOError as e:
        raise IOError(f"写入 CSV 文件失败: {e}")


class DouyinScraper:
    """抖音网页数据抓取器，基于 DrissionPage。"""

    # ---- 全局运行参数（按需修改） ----
    JS_TIMEOUT = 10                # 每次 run_js 的超时时间（秒）- 从3秒增加到10秒，减少误判超时
    JS_RETRY = 3                   # 超时或返回空时的自动重试次数（每个阶段）- 从2次增加到3次，提高成功率
    SLEEP_BETWEEN_TRIES = 0.8      # 单页内部重试的间隔（秒）
    SLEEP_BETWEEN_PAGES = 1.5      # 翻页间隔，降低风控概率

    def __init__(self):
        """初始化 Chromium 实例并配置个人数据目录.

        Args:
            user_data_path: 浏览器用户数据路径（新目录，避免历史缓存干扰）
        """
        # options = ChromiumOptions()
        # options.set_user_data_path(user_data_path)
        # self.dp = Chromium(options).latest_tab
        self.dp = Chromium().latest_tab
        self.logger = setup_logger("DouyinScraper")

    # ---------------- 工具方法 ----------------

    def _to_json(self, body) -> dict:
        """将 response.body 统一转换为 dict，增加异常处理避免JSON解析错误导致程序崩溃."""
        if not body:
            return {}

        try:
            if isinstance(body, (bytes, bytearray)):
                return json.loads(body.decode('utf-8', 'ignore'))
            if isinstance(body, str):
                return json.loads(body)
            return body or {}
        except json.JSONDecodeError as e:
            # JSON解析错误不应该导致程序崩溃，记录警告并返回空字典
            self.logger.warning(f"JSON解析失败: {e}")
            return {}
        except Exception as e:
            # 其他异常也进行处理
            self.logger.error(f"数据处理异常: {e}")
            return {}

    @staticmethod
    def _set_query_params(url: str, **kw) -> str:
        """在 URL 上修改或添加查询参数."""
        u = urlsplit(url)
        q = dict(parse_qsl(u.query, keep_blank_values=True))
        for k, v in kw.items():
            if v is not None:
                q[k] = str(v)
        return urlunsplit((u.scheme, u.netloc, u.path, urlencode(q, doseq=True), u.fragment))

    def _classify_error(self, error_msg: str, data: dict = None) -> str:
        """错误分类，避免将技术错误误判为滑块验证"""
        error_str = str(error_msg).lower()

        if "expecting value" in error_str or "json" in error_str:
            return "json_parse_error"    # JSON解析错误
        elif "timeout" in error_str:
            return "timeout_error"       # 超时错误
        elif "captcha" in error_str or "验证" in error_str:
            return "captcha_required"    # 滑块验证
        elif data is None or (isinstance(data, dict) and not data):
            return "empty_response"      # 空响应
        else:
            return "unknown_error"       # 未知错误

    def _should_trigger_captcha(self, data: dict, error_msg: str = "") -> bool:
        """判断是否真的需要滑块验证，避免将技术错误误判为人工验证需求"""
        error_type = self._classify_error(error_msg, data)

        # JSON解析错误、超时错误不是滑块问题
        if error_type in ["json_parse_error", "timeout_error"]:
            return False

        # 明确的验证需求
        if error_type == "captcha_required":
            return True

        # 空响应可能需要验证，但要更谨慎
        if error_type == "empty_response":
            return not data  # 只有在确实没有数据时才触发

        return False  # 其他情况不触发验证

    def _wait_user_to_solve(self, scene: str = '操作'):
        """在终端提示用户去浏览器完成滑块/安全验证，完成后回车继续。"""
        self.logger.warning(f"触发滑块/安全验证 - 场景: {scene}")
        print(f"\n[提示] 可能触发了滑块/安全验证（{scene}）。")
        print("        请切换到已打开的浏览器页面完成验证。")
        print("        完成后回到本窗口，按 Enter 继续；输入 q 然后回车可终止。\n")
        ack = input("完成验证后按 Enter 继续（或输入 q 退出）：").strip().lower()
        if ack == 'q':
            self.logger.info("用户选择退出任务")
            raise RuntimeError("用户取消，终止当前任务。")
        self.logger.info(f"用户完成验证，继续执行 - 场景: {scene}")

    def _fetch_json_via_js(self, url: str, scene: str) -> dict | None:
        """统一的 JS 拉取包装：任何 JS 超时/空返回都按触发滑块处理，并能在完成验证后继续原进度。

        流程：
            - 尝试 JS_RETRY 次，run_js 超时时间 JS_TIMEOUT；
            - 仍失败 → 提示用户过滑块；
            - 再尝试 JS_RETRY 次；
            - 若还失败，则抛错。
        """
        js = f"""
return (async () => {{
  const r = await fetch('{url}', {{ credentials: 'include' }});
  try {{ return await r.json(); }} catch (e) {{ return null; }}
}})()
"""

        # 阶段 1：自动重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 视为滑块
        self._wait_user_to_solve(scene)

        # 阶段 2：完成验证后再次重试
        for _ in range(self.JS_RETRY):
            try:
                data = self.dp.run_js(js, timeout=self.JS_TIMEOUT)
            except Exception:
                data = None
            if data:
                return data
            time.sleep(self.SLEEP_BETWEEN_TRIES)

        raise RuntimeError(f"多次尝试后仍未获取到数据（{scene}）。")

    # ---------------- 业务方法 ----------------

    def fetch_sec_uid(self, douyin_id: str) -> str:
        """通过搜索建议接口获取用户的 sec_uid。"""
        self.logger.info(f"开始获取用户 sec_uid - 抖音ID: {douyin_id}")

        self.dp.listen.clear()
        self.dp.listen.start(f"/aweme/v1/web/api/suggest_words/?query={douyin_id}")
        self.dp.get(f'https://www.douyin.com/search/{douyin_id}')
        pkt = next(self.dp.listen.steps(count=1))
        self.dp.listen.pause(True)

        data = self._to_json(pkt.response.body)
        msg = jmespath.search('data[0].params.extra_info.msg', data)
        payload = json.loads(msg) if msg else {}
        sec_uid = jmespath.search('src_query.sug_sec_uid', payload)

        if not sec_uid:
            self.logger.error(f"未找到 sec_uid - 抖音ID: {douyin_id}")
            raise RuntimeError("未在 suggest_words 响应中找到 sec_uid。")

        self.logger.info(f"成功获取 sec_uid: {sec_uid} - 抖音ID: {douyin_id}")
        return sec_uid

    def fetch_user_profile(self, sec_uid: str) -> dict:
        """获取用户基本信息."""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/user/profile/other/?device_platform=webapp")
        self.dp.get(f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main')
        pkt = next(self.dp.listen.steps(count=1))
        self.dp.listen.pause(True)

        profile = self._to_json(pkt.response.body).get('user', {}) or {}
        return {
            'nickname': profile.get('nickname'),
            'uid': profile.get('uid'),
            'unique_id': profile.get('unique_id'),
            'followers': profile.get('mplatform_followers_count'),
            'following': profile.get('following_count'),
            'signature': profile.get('signature'),
            'aweme_count': profile.get('aweme_count'),
            'favoriting_count': profile.get('favoriting_count'),
        }

    def fetch_followers(self,
                        sec_uid: str,
                        max_items: int = 5000,
                        page_count: int = 20) -> List[Dict]:
        """抓取用户粉丝列表（PC Web），时间游标分页。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/user/follower/list/")

        # 进入主页 -> 点击“粉丝”，触发首包
        self.dp.get(f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main")
        locator = 'x://div[@data-e2e="user-info-fans"]'
        self.dp.wait.ele_displayed(locator, timeout=15)
        self.dp.ele(locator).click()

        try:
            # ---- 首包 ----
            first_req_url, first_data = None, None
            for _ in range(self.JS_RETRY):  # 用相同的次数语义以保持一致
                pkt = next(self.dp.listen.steps(count=1))
                first_req_url = pkt.request.url
                first_data = self._to_json(pkt.response.body)
                if (first_data or {}).get('followers'):
                    break
                time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                self._wait_user_to_solve('获取粉丝列表（首包）')
                for _ in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    first_req_url = pkt.request.url
                    first_data = self._to_json(pkt.response.body)
                    if (first_data or {}).get('followers'):
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((first_data or {}).get('followers')):
                raise RuntimeError("多次尝试后仍未获取到粉丝首包数据，请稍后再试。")

            # ---- 收集与去重 ----
            collected: List[Dict] = []
            seen: Set[str] = set()

            def _extend(items: List[Dict]) -> int:
                added = 0
                for it in items or []:
                    k = str(it.get('uid') or it.get('sec_uid') or it.get('unique_id') or id(it))
                    if k in seen:
                        continue
                    seen.add(k)
                    collected.append(it)
                    added += 1
                return added

            # ---- 处理首包 ----
            page_followers = first_data.get('followers') or []
            added = _extend(page_followers)
            has_more = bool(first_data.get('has_more'))
            next_max_time = int(first_data.get('min_time') or 0)
            last_min_time = next_max_time
            stuck_rounds = 0

            print(f"[首包] items={len(page_followers)} (+{added}) "
                  f"has_more={has_more} min_time={first_data.get('min_time')} "
                  f"max_time={first_data.get('max_time')} total={first_data.get('total')}")

            # ---- 翻页循环 ----
            while has_more and len(collected) < max_items:
                query_url = self._set_query_params(
                    first_req_url,
                    max_time=max(0, next_max_time),
                    count=page_count
                )

                page = self._fetch_json_via_js(query_url, scene='获取粉丝列表（翻页）')
                items = (page or {}).get('followers') or []
                page_added = _extend(items)
                has_more = bool((page or {}).get('has_more'))

                print(f"[粉丝翻页] items={len(items)} (+{page_added}) "
                      f"has_more={has_more} min_time={(page or {}).get('min_time')} "
                      f"max_time={(page or {}).get('max_time')} 粉丝数量累积={len(collected)}")
                if not has_more:
                    break

                page_min_time = int((page or {}).get('min_time') or 0)

                # 游标卡住 -> -1 纠偏；连卡两次退出
                if page_min_time >= last_min_time:
                    stuck_rounds += 1
                    next_max_time = max(0, last_min_time - 1)
                    if stuck_rounds >= 2:
                        print("[提示] 时间游标连续未推进，认为已到尽头，提前结束以避免死循环。")
                        break
                else:
                    next_max_time = page_min_time
                    last_min_time = page_min_time
                    stuck_rounds = 0

                time.sleep(self.SLEEP_BETWEEN_PAGES)
            follower_list_json = json.dumps(collected, ensure_ascii=False, indent=2)
            # 处理json数据,只保留特定字段
            follower_list = json.loads(follower_list_json)

            # 定义需要保留的字段（支持嵌套字段）
            keep_fields = {
                '用户UID': 'uid',
                '用户sec_uid': 'sec_uid',
                '用户抖音号': 'unique_id',
                '用户昵称': 'nickname',
                '用户签名': 'signature',
                '用户头像': 'avatar_thumb.url_list[0]',
                '粉丝数': 'mplatform_followers_count',
                '关注数': 'following_count',
                '作品数': 'aweme_count',
                '获赞数': 'favoriting_count',
                '总被赞数': 'total_favorited',
                '是否官方账号': 'is_gov_media_vip',
                '是否为明星': 'is_star',
                '是否认证': 'is_verified',
                '认证类型': 'verification_type',
                '认证信息': 'custom_verify',
                '企业认证原因': 'enterprise_verify_reason'
            }

            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 只保留指定字段，创建新的清理后的列表
            cleaned_followers = []
            for follower in follower_list:
                cleaned_follower = {}
                for new_field, path in keep_fields.items():
                    cleaned_follower[new_field] = get_nested_value(follower, path)
                cleaned_followers.append(cleaned_follower)
            

            return cleaned_followers[:max_items]

        finally:
            self.dp.listen.pause(True)

    def fetch_video_info(self,
                        video_id: str) -> Dict:
        """抓取视频信息。"""
        self.logger.debug(f"开始获取视频详情 - ID: {video_id}")

        # 性能瓶颈：每个视频都重新设置监听器 (耗时1-2秒)
        # 优化建议：改为一次性设置监听，在批处理开始前设置，多个视频复用
        # 推荐模式：在 fetch_favorites() 开始时设置一次监听即可
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")
        video_url = f"https://www.douyin.com/video/{video_id}"

        try:
            # 打开视频页面 → 获取首包数据
            self.logger.debug(f"访问视频页面: {video_url}")
            self.dp.get(video_url)

            # ---- 首包数据获取 ----
            data = None
            last_error = ""
            # 性能瓶颈：使用低效的 listen.steps() API
            # 优化建议：改用 packet = self.dp.listen.wait(timeout=10, raise_err=True)
            # 新API内置超时处理和异常管理，无需手动重试循环
            for retry_count in range(self.JS_RETRY):
                try:
                    pkt = next(self.dp.listen.steps(count=1))
                    response_size = len(pkt.response.body) if pkt.response.body else 0
                    self.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        self.logger.debug(f"成功获取视频详情数据 - ID: {video_id}")
                        break
                    else:
                        self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
                except Exception as e:
                    # 记录最后一次错误，用于后续错误分类
                    last_error = str(e)
                    error_type = self._classify_error(last_error, data)
                    self.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

                time.sleep(self.SLEEP_BETWEEN_TRIES)

            # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
            # 只有在确实需要滑块验证时才触发人工验证
            if not ((data or {}).get('aweme_detail')):
                if self._should_trigger_captcha(data, last_error):
                    self.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
                    self._wait_user_to_solve('获取视频信息（首包）')

                    for retry_count in range(self.JS_RETRY):
                        self.dp.refresh()
                        pkt = next(self.dp.listen.steps(count=1))
                        response_size = len(pkt.response.body) if pkt.response.body else 0
                        self.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                        data = self._to_json(pkt.response.body)
                        if (data or {}).get('aweme_detail'):
                            self.logger.info(f"验证后成功获取视频详情数据 - ID: {video_id}")
                            break
                        time.sleep(self.SLEEP_BETWEEN_TRIES)
                else:
                    # 技术错误，不需要人工验证，直接记录并继续
                    error_type = self._classify_error(last_error, data)
                    self.logger.info(f"检测到技术错误({error_type})，跳过滑块验证 - ID: {video_id}")

            video_detail = (data or {}).get('aweme_detail')
            if not video_detail:
                self.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
                raise RuntimeError("多次尝试后仍未获取到视频详情数据，请稍后再试。")

            # 保留特定字段（根据实际响应体结构调整）
            keep_fields = {
                '视频id': 'aweme_id',
                '视频描述': 'desc',
                '创建时间': 'create_time',
                '视频时长': 'duration',
                '视频作者UID': 'author.uid',
                '视频作者昵称': 'author.nickname',
                '视频作者抖音号': 'author.unique_id',
                '视频作者头像': 'author.avatar_thumb.url_list[0]',
                '视频作者签名': 'author.signature',
                '视频作者粉丝数': 'author.follower_count',
                '视频作者关注数': 'author.following_count',
                '视频作者获赞数': 'author.favoriting_count',
                '视频作者总被赞数': 'author.total_favorited',
                '视频作者认证信息': 'author.custom_verify',
                '视频作者企业认证原因': 'author.enterprise_verify_reason',
                '点赞数': 'statistics.digg_count',
                '评论数': 'statistics.comment_count',
                '分享数': 'statistics.share_count',
                '收藏数': 'statistics.collect_count',
                '播放数': 'statistics.play_count',
                '音乐标题': 'music.title',
                '音乐作者': 'music.author',
                '视频链接': 'share_info.share_url'
            }

            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 处理单个视频对象，提取指定字段
            cleaned_video = {}
            for new_field, path in keep_fields.items():
                if new_field == '视频链接':
                    # 特殊处理视频链接字段，使用构建的URL
                    cleaned_video[new_field] = video_url
                else:
                    cleaned_video[new_field] = get_nested_value(video_detail, path)

            return cleaned_video

        finally:
            self.dp.listen.pause(True)

    def _fetch_video_info_optimized(self, video_id: str) -> Dict:
        """优化版本的视频信息获取，复用已设置的监听器，避免重复设置监听器的性能开销"""
        self.logger.debug(f"开始获取视频详情（优化版本） - ID: {video_id}")

        video_url = f"https://www.douyin.com/video/{video_id}"

        # 直接访问页面，无需重新设置监听器
        self.logger.debug(f"访问视频页面: {video_url}")
        self.dp.get(video_url)

        # ---- 首包数据获取 ----
        data = None
        last_error = ""

        for retry_count in range(self.JS_RETRY):
            try:
                pkt = next(self.dp.listen.steps(count=1))
                response_size = len(pkt.response.body) if pkt.response.body else 0
                self.logger.debug(f"获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                data = self._to_json(pkt.response.body)
                if (data or {}).get('aweme_detail'):
                    self.logger.debug(f"成功获取视频详情数据 - ID: {video_id}")
                    break
                else:
                    self.logger.warning(f"响应中无 aweme_detail 字段 - ID: {video_id}, 重试: {retry_count + 1}")
            except Exception as e:
                # 记录最后一次错误，用于后续错误分类
                last_error = str(e)
                error_type = self._classify_error(last_error, data)
                self.logger.warning(f"获取响应包异常 - ID: {video_id}, 错误类型: {error_type}, 错误: {e}, 重试: {retry_count + 1}")

            time.sleep(self.SLEEP_BETWEEN_TRIES)

        # 改进的错误判断逻辑：区分JSON解析错误和真正的滑块验证需求
        if not ((data or {}).get('aweme_detail')):
            if self._should_trigger_captcha(data, last_error):
                self.logger.warning(f"检测到需要滑块验证 - ID: {video_id}")
                self._wait_user_to_solve('获取视频信息（首包）')

                for retry_count in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    response_size = len(pkt.response.body) if pkt.response.body else 0
                    self.logger.debug(f"验证后获取响应包 - 大小: {response_size} bytes, 重试次数: {retry_count + 1}")

                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_detail'):
                        self.logger.info(f"验证后成功获取视频详情数据 - ID: {video_id}")
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)
            else:
                # 技术错误，不需要人工验证，返回基本信息
                error_type = self._classify_error(last_error, data)
                self.logger.info(f"检测到技术错误({error_type})，返回基本信息 - ID: {video_id}")
                return self._get_basic_video_info(video_id)

        video_detail = (data or {}).get('aweme_detail')
        if not video_detail:
            self.logger.error(f"多次尝试后仍未获取到视频详情数据 - ID: {video_id}")
            return self._get_basic_video_info(video_id)

        # 处理视频详情数据（与原方法相同的逻辑）
        return self._process_video_detail(video_detail, video_id)

    def _get_basic_video_info(self, video_id: str) -> Dict:
        """当无法获取详细信息时，返回基本的视频信息"""
        return {
            '视频id': video_id,
            '视频描述': '获取失败',
            '创建时间': '',
            '视频时长': 0,
            '视频作者UID': '未知',
            '视频作者昵称': '未知',
            '视频作者抖音号': '未知',
            '视频作者头像': '',
            '视频作者签名': '未知',
            '视频作者粉丝数': 0,
            '视频作者关注数': 0,
            '视频作者获赞数': 0,
            '视频作者总被赞数': 0,
            '视频作者认证信息': '',
            '视频作者企业认证原因': '',
            '点赞数': 0,
            '评论数': 0,
            '分享数': 0,
            '收藏数': 0,
            '播放数': 0,
            '音乐标题': '未知',
            '音乐作者': '未知',
            '视频链接': f"https://www.douyin.com/video/{video_id}"
        }

    def _process_video_detail(self, video_detail: dict, video_id: str) -> Dict:
        """处理视频详情数据，提取需要的字段"""
        video_url = f"https://www.douyin.com/video/{video_id}"

        # 定义需要保留的字段及其路径映射（恢复完整字段列表）
        keep_fields = {
            '视频id': 'aweme_id',
            '视频描述': 'desc',
            '创建时间': 'create_time',
            '视频时长': 'duration',
            '视频作者UID': 'author.uid',
            '视频作者昵称': 'author.nickname',
            '视频作者抖音号': 'author.unique_id',
            '视频作者头像': 'author.avatar_thumb.url_list[0]',
            '视频作者签名': 'author.signature',
            '视频作者粉丝数': 'author.follower_count',
            '视频作者关注数': 'author.following_count',
            '视频作者获赞数': 'author.favoriting_count',
            '视频作者总被赞数': 'author.total_favorited',
            '视频作者认证信息': 'author.custom_verify',
            '视频作者企业认证原因': 'author.enterprise_verify_reason',
            '点赞数': 'statistics.digg_count',
            '评论数': 'statistics.comment_count',
            '分享数': 'statistics.share_count',
            '收藏数': 'statistics.collect_count',
            '播放数': 'statistics.play_count',
            '音乐标题': 'music.title',
            '音乐作者': 'music.author',
            '视频链接': None  # 特殊处理
        }

        def get_nested_value(obj, path, default=''):
            """安全地获取嵌套字典的值"""
            if not path:
                return default
            try:
                parts = re.split(r'[\.\[\]]', path)
                parts = [p for p in parts if p]
                current = obj
                for part in parts:
                    if part.isdigit():
                        current = current[int(part)]
                    else:
                        current = current[part]
                return current if current is not None else default
            except (KeyError, TypeError, AttributeError, IndexError):
                return default

        # 处理单个视频对象，提取指定字段
        cleaned_video = {}
        for new_field, path in keep_fields.items():
            if new_field == '视频链接':
                cleaned_video[new_field] = video_url
            else:
                cleaned_video[new_field] = get_nested_value(video_detail, path)

        return cleaned_video



    def fetch_favorites(self, sec_uid: str, max_items: int = 200) -> List[Dict]:
        """获取用户点赞的视频列表（演示），同样按“任何 JS 超时 → 滑块处理”。"""
        self.dp.listen.clear()
        self.dp.listen.start("/aweme/v1/web/aweme/favorite/?device_platform=webapp")
        like_url = f"https://www.douyin.com/user/{sec_uid}?from_tab_name=main&showTab=like"

        try:
            # 打开点赞页 → 首包
            self.dp.get(like_url)

            first_req, data = None, None
            for _ in range(self.JS_RETRY):
                pkt = next(self.dp.listen.steps(count=1))
                first_req = pkt.request.url
                data = self._to_json(pkt.response.body)
                if (data or {}).get('aweme_list'):
                    break
                time.sleep(self.SLEEP_BETWEEN_TRIES)

            if not ((data or {}).get('aweme_list')):
                self._wait_user_to_solve('获取喜欢列表（首包）')
                for _ in range(self.JS_RETRY):
                    self.dp.refresh()
                    pkt = next(self.dp.listen.steps(count=1))
                    first_req = pkt.request.url
                    data = self._to_json(pkt.response.body)
                    if (data or {}).get('aweme_list'):
                        break
                    time.sleep(self.SLEEP_BETWEEN_TRIES)

            items = (data or {}).get('aweme_list') or []
            if not items:
                raise RuntimeError("多次尝试后仍未获取到喜欢首包数据，请稍后再试。")

            has_more = data.get('has_more', 0)
            cursor = data.get('max_cursor', 0)
            print(f"[首包-喜欢] items={len(items)} has_more={has_more} "
                  f"min_cursor={data.get('min_cursor')} max_cursor={data.get('max_cursor')}")

            # 翻页
            while has_more and len(items) < max_items:
                next_url = self._set_query_params(first_req, max_cursor=cursor, min_cursor=0)
                page_data = self._fetch_json_via_js(next_url, scene='获取喜欢列表（翻页）')

                page_items = (page_data or {}).get('aweme_list') or []
                items.extend(page_items)
                has_more = (page_data or {}).get('has_more', 0)
                cursor = (page_data or {}).get('max_cursor', 0)

                print(f"[喜欢翻页] items={len(page_items)} has_more={has_more} "
                      f"min_cursor={(page_data or {}).get('min_cursor')} "
                      f"max_cursor={(page_data or {}).get('max_cursor')} 累积={len(items)}")

                time.sleep(self.SLEEP_BETWEEN_PAGES)

            
            # 处理视频数据，获取每个视频的详细信息
            def get_nested_value(data, path, default=''):
                """获取嵌套字典中的值，支持点号分隔的路径和数组索引"""
                try:
                    current = data
                    # 处理路径中的数组索引，如 avatar_thumb.url_list[0]
                    import re
                    parts = re.split(r'[\.\[\]]', path)
                    parts = [p for p in parts if p]  # 移除空字符串

                    for part in parts:
                        if part.isdigit():  # 数组索引
                            current = current[int(part)]
                        else:  # 字典键
                            current = current[part]
                    return current if current is not None else default
                except (KeyError, TypeError, AttributeError, IndexError):
                    return default

            # 获取每个视频的详细信息
            self.logger.info(f"开始获取 {len(items)} 个视频的详细信息")
            detailed_favorites = []
            success_count = 0
            fail_count = 0

            # 优化：为批量视频处理设置一次性监听器，避免重复设置
            self.logger.info("设置视频详情监听器（批量处理优化）")
            self.dp.listen.clear()
            self.dp.listen.start("/aweme/v1/web/aweme/detail/?device_platform=webapp")

            # 架构性能瓶颈：串行处理架构，无并发优化
            # 总处理时间 = (单视频时间 + 1.5s延迟) × 视频数量
            # 优化建议：使用DrissionPage 4.x的多标签页支持实现有限并发
            for idx, item in enumerate(items, 1):
                video_id = get_nested_value(item, 'aweme_id')
                if video_id:
                    start_time = time.time()
                    try:
                        self.logger.debug(f"[{idx}/{len(items)}] 开始获取视频详情 - ID: {video_id}")

                        # 性能优化：使用优化版本的视频信息获取，复用已设置的监听器
                        # 获取视频详细信息
                        video_detail = self._fetch_video_info_optimized(video_id)
                        detailed_favorites.append(video_detail)

                        elapsed_time = time.time() - start_time
                        success_count += 1

                        self.logger.info(f"[{idx}/{len(items)}] 成功获取视频详情 - ID: {video_id}, 耗时: {elapsed_time:.2f}s")
                        print(f"已获取视频 {video_id} 的详细信息 (耗时: {elapsed_time:.2f}s)")

                        # 性能影响：每个视频后强制等待1.5秒
                        # 防风控设计：降低被检测为高频请求的概率
                        # 添加延迟避免请求过快
                        time.sleep(self.SLEEP_BETWEEN_PAGES)

                    except Exception as e:
                        elapsed_time = time.time() - start_time
                        fail_count += 1

                        self.logger.error(f"[{idx}/{len(items)}] 获取视频详情失败 - ID: {video_id}, 错误: {e}, 耗时: {elapsed_time:.2f}s")
                        print(f"获取视频 {video_id} 详情失败: {e} (耗时: {elapsed_time:.2f}s)")

                        # 如果获取详情失败，使用基础信息
                        basic_info = {
                            '视频id': get_nested_value(item, 'aweme_id'),
                            '视频描述': get_nested_value(item, 'desc'),
                            '创建时间': get_nested_value(item, 'create_time'),
                            '视频作者昵称': get_nested_value(item, 'author.nickname'),
                        }
                        detailed_favorites.append(basic_info)
                        continue

            self.logger.info(f"视频详情获取完成 - 成功: {success_count}, 失败: {fail_count}, 总计: {len(items)}")

            return detailed_favorites[:max_items]

        finally:
            self.dp.listen.pause(True)



    def close(self):
        """退出并关闭浏览器."""
        self.dp.browser.quit()


def main():
    """主函数：演示抓取粉丝列表与喜欢列表。"""
    # 初始化日志系统
    logger = setup_logger("Main", logging.INFO)
    logger.info("=" * 60)
    logger.info("抖音数据抓取程序启动")
    logger.info("=" * 60)

    # scraper = DouyinScraper(user_data_path=r'D:\temp\dp_profile_clean')
    Max_follower_count = 30
    Max_favorite_count = 30
    #是否导出粉丝信息--json
    is_export_follower_json = True
    #是否导出粉丝信息--csv
    is_export_follower_csv = True
    #是否导出喜欢视频信息--json
    is_export_favorite_json = True
    #是否导出喜欢视频信息--csv
    is_export_favorite_csv = True

    logger.info(f"配置参数 - 最大粉丝数: {Max_follower_count}, 最大喜欢数: {Max_favorite_count}")

    scraper = DouyinScraper()
    try:
        douyin_id = "96967475948"
        logger.info(f"开始处理用户 - 抖音ID: {douyin_id}")

        sec_uid = scraper.fetch_sec_uid(douyin_id)
        print(f"sec_uid: {sec_uid}\n{'='*60}")

        logger.info("开始获取用户基本信息")
        profile = scraper.fetch_user_profile(sec_uid)
        for k, v in profile.items():
            print(f"{k}: {v}")
        print('=' * 60)
        logger.info("用户基本信息获取完成")

        # # 粉丝列表（时间游标分页）
        # followers = scraper.fetch_followers(
        #     sec_uid=sec_uid,
        #     max_items=Max_follower_count,     # 按需调整
        #     page_count=20
        # )
        # print(f"\n粉丝抓取完成：{len(followers)} 条。")
        # if is_export_follower_json:
        #     # 保存粉丝详情到 JSON 文件
        #     json_filename = f"followers_details_{int(time.time())}.json"
        #     with open(json_filename, 'w', encoding='utf-8') as f:
        #         json.dump(followers, f, ensure_ascii=False, indent=2)
        #     print(f"粉丝详情已保存到 JSON 文件: {json_filename} (共{len(followers)}条记录)")

        # if is_export_follower_csv:
        #     # 导出粉丝详情到 CSV 文件
        #     csv_filename = f"followers_details_{int(time.time())}.csv"
        #     json_to_csv(followers, csv_filename)




        # 喜欢列表（可选）
        logger.info(f"开始获取喜欢列表 - 最大数量: {Max_favorite_count}")
        start_time = time.time()

        favorites = scraper.fetch_favorites(
            sec_uid=sec_uid,
            max_items=Max_favorite_count
        )

        elapsed_time = time.time() - start_time
        logger.info(f"喜欢列表获取完成 - 数量: {len(favorites)}, 总耗时: {elapsed_time:.2f}s")
        print(f"喜欢抓取完成：{len(favorites)} 条。")

        if is_export_favorite_json:
            logger.info("开始保存喜欢列表到 JSON 文件")
            # 保存喜欢列表到 JSON 文件
            favorites_json_filename = f"favorites_details_{int(time.time())}.json"
            with open(favorites_json_filename, 'w', encoding='utf-8') as f:
                json.dump(favorites, f, ensure_ascii=False, indent=2)
            logger.info(f"JSON 文件保存完成: {favorites_json_filename}")
            print(f"喜欢详情已保存到 JSON 文件: {favorites_json_filename} (共{len(favorites)}条记录)")
        if is_export_favorite_csv:
            # 导出喜欢列表到 CSV 文件
            favorites_csv_filename = f"favorites_details_{int(time.time())}.csv"
            json_to_csv(favorites, favorites_csv_filename)

        # 示例：获取单个视频详情
        # video_id = "7531226211713289530"  # 示例视频ID
        # try:
        #     video_info = scraper.fetch_video_info(video_id)
        #     print(f"视频详情获取完成：{video_info.get('视频描述', '')}")
        #
        #     # 保存视频详情到文件
        #     video_json_filename = f"video_detail_{video_id}_{int(time.time())}.json"
        #     with open(video_json_filename, 'w', encoding='utf-8') as f:
        #         json.dump(video_info, f, ensure_ascii=False, indent=2)
        #     print(f"视频详情已保存到: {video_json_filename}")
        # except Exception as e:
        #     print(f"获取视频详情失败: {e}")

    except Exception as e:
        logger.error(f"程序执行出现异常: {e}", exc_info=True)
        raise
    finally:
        # scraper.close()
        logger.info("=" * 60)
        logger.info("抖音数据抓取程序结束")
        logger.info("=" * 60)
        print(f"任务完成")


if __name__ == "__main__":
    main()
